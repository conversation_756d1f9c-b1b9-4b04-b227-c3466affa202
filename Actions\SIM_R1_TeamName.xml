<?xml version="1.0" encoding="utf-8" ?>

<Action mainAreaLayout="../../../layout/mainLayout_SIM">
  <Include name="Header_SIM"></Include>  


  <Component type="FormInputs" customJS="true">
    <![CDATA[{
      templateInEvent: "html/formInputs.dot",
      css: "styles/formInputs.css",
      animate: "fadeIn",
      header: "!{TeamName_Header}",
      instructions: "",
      
      valign: false,
      orientation: "",
      content: {
        title: "!{}",
        body: "!{TeamName_Body}"
      },  

      remaining: "!{InputRemaining}",
      inputRight: "!{InputRight}",
      inputWrong: "!{InputWrong}",
      
      myName: "Q_My_Name",

      inputs: [
        {
          name: "name",
          type: "text", required: true,
          bind: "Q_My_Name",
          label: "!{TeamName_Placeholder}",
          length: "!{TeamName_length}",
          icon: "person",
          hideRemaining: true,
          helper: false,
          grid: "s12"
        },
        {
          name: "avatar",
          type: "select", required: true,
          bind: "Q_My_Avatar",
          label: "!{TeamName_Avatar}",
          icon: "face",
          grid: "s12",
          title: "!{TeamName_Avatar_select}",
          json: "/content/avatars.json",
          icons: "/images/avatar/",
          helper: false,
          options: []
        }
      ],

      submitBtn: {
        clearAfter: false,
        disabled: true,
        label: "!{InputForm}",
        toast: "!{InputForm_OK}",
        showId: "navButton",
        targetSection: ""
      },
      
      follower_bind: "Q_My_Name",
      follower_suffix: " - !{Header_Follower}",
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower"]
      
    }]]>
  </Component> 


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: true, _showDelay: "5000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "SIM_CaseStudy",
          targetSection: "",
          label: "!{Navigation_start}",
          tooltip: "!{Navigation_start_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>







</Action>